import Container, { Service } from 'typedi';
import { HttpError } from 'routing-controllers';
import moment from 'moment';
import Logger from '@app/loaders/logger';
import { ICurrentUser } from '@app/interfaces';
import { AssignFarmingPlanTaskDtoArray, AssignFarmingPlanTaskDto, WarehouseItemTaskUsedDto, ProductionQuantityDto, CreateFarmingPlanTaskDto } from '../task/FarmingPlanTask.dto';
import { FilterTuple } from '@app/utils/helpers/queryHelpter';
import { FarmingPlanTaskArray } from '@app/interfaces/ITaskAllResource';
import { FarmingPlanTask } from '@app/orm/entities/farmingPlan/FarmingPlanTask';
import { TaskService } from '@app/modules/farming-plan/task/TaskService';
import { AppDataSource } from '@app/orm/dataSource';
import { In } from 'typeorm';
import { applyQueryFilters } from '@app/utils/helpers/queryHelpter';
import { isVietPlantsTenant } from '@app/utils/vietplants/vietplants-utils';
import { FarmingPlanTaskTemplate } from '@app/orm/entities/farmingPlan/template/FarmingPlanTaskTemplate';
import { ItemTaskTemplate } from '@app/orm/entities/farmingPlan/template/ItemTaskTemplate';
import { ProductionTaskTemplate } from '@app/orm/entities/farmingPlan/template/ProductionTaskTemplate';
import { TaskItemTransfer } from '@app/orm/entities/farmingPlan/taskItem/TaskItemTransfer';
import { ProductionQuantity } from '@app/orm/entities/farmingPlan/taskItem/ProductionQuantity';
import { WarehouseItemTaskUsed } from '@app/orm/entities/farmingPlan/taskItem/WarehouseItemTaskUsed';
import { ProductionPlanAllocationService } from '../../production-plan/ProductionPlanAllocationService';
import { ProductionPlanYieldExpectedOutput, ProductionPlan } from '@app/orm/entities/productionPlan/ProductionPlan';
import { TaskItemTransferService } from '../vietplants-item-transfer/TaskItemTransferService';
import { LabelPrintManagementService } from '../vietplants-label-print-management/LabelPrintManagementService';

@Service()
export class FarmingPlanTaskService {
  private oldTaskService: TaskService;
  private taskItemTransferService: TaskItemTransferService;
  private labelPrintManagementService: LabelPrintManagementService;
  private allocationService: ProductionPlanAllocationService;
  private yieldExpectedOutputRepository = AppDataSource.getRepository(ProductionPlanYieldExpectedOutput);
  private productionPlanRepository = AppDataSource.getRepository(ProductionPlan);

  constructor() {
    this.oldTaskService = Container.get(TaskService);
    this.taskItemTransferService = Container.get(TaskItemTransferService);
    this.labelPrintManagementService = Container.get(LabelPrintManagementService);
    this.allocationService = Container.get(ProductionPlanAllocationService);
  }

  /**
   * Gets a task by its ID
   * @param user Current user
   * @param taskId Task ID
   * @returns FarmingPlanTask
   */
  async getTaskById(user: ICurrentUser, taskId: string): Promise<FarmingPlanTask> {
    try {
      const repo = AppDataSource.getRepository(FarmingPlanTask);
      const task = await repo.findOne({
        where: { name: taskId },
        relations: ['farmingPlanState', 'assignedUser', 'department', 'environmentTemplate', 'template']
      });

      // Verify the task belongs to the current customer
      if (task && user.customer?.id && task.owner !== user.customer.id.toString()) {
        throw new HttpError(403, `Unauthorized access to task ${taskId}`);
      }

      if (!task) {
        throw new HttpError(404, `Task ${taskId} not found`);
      }

      return task;
    } catch (error) {
      Logger.error(`Error getting task by ID:`, error);
      throw error;
    }
  }

  /**
   * Gets all tasks in a chain starting from a specific task
   * This method has been updated to use the new TaskItemTransfer structure
   * @param user Current user
   * @param taskId Starting task ID
   * @returns Array of tasks in the chain
   */
  async getTaskChain(user: ICurrentUser, taskId: string): Promise<FarmingPlanTask[]> {
    try {
      const startTask = await this.getTaskById(user, taskId);
      const taskChain: FarmingPlanTask[] = [startTask];

      // If task has chain IDs stored, fetch all tasks in the chain (legacy support)
      if (startTask.task_chain_ids) {
        try {
          const chainIds = JSON.parse(startTask.task_chain_ids) as string[];
          const repo = AppDataSource.getRepository(FarmingPlanTask);

          const chainTasks = await repo.find({
            where: {
              name: In(chainIds)
            },
            relations: ['farmingPlanState', 'assignedUser', 'template']
          });

          // Filter tasks to only include those belonging to the current customer
          const customerTasks = chainTasks.filter(task =>
            !user.customer?.id || task.owner === user.customer.id.toString()
          );

          // Use filtered tasks for chain processing
          const filteredChainTasks = customerTasks.length > 0 ? customerTasks : chainTasks;

          // Sort tasks according to the chain order
          const sortedChainTasks = chainIds.map(id =>
            chainTasks.find(task => task.name === id)
          ).filter(task => !!task) as FarmingPlanTask[];

          return sortedChainTasks;
        } catch (e) {
          Logger.error(`Error parsing task chain IDs:`, e);
        }
      }

      // Use the new item transfer relationships
      // First, go backward to find all parent tasks
      const backwardChain: FarmingPlanTask[] = [];
      const processedParentIds = new Set<string>();
      let currentLevelTasks = [startTask];

      while (currentLevelTasks.length > 0) {
        const nextLevelTasks: FarmingPlanTask[] = [];

        for (const task of currentLevelTasks) {
          const parentTasks = await this.taskItemTransferService.getParentTasks(user, task.name);

          for (const parentTask of parentTasks) {
            if (!processedParentIds.has(parentTask.name)) {
              backwardChain.unshift(parentTask); // Add to the beginning of the array
              processedParentIds.add(parentTask.name);
              nextLevelTasks.push(parentTask);
            }
          }
        }

        currentLevelTasks = nextLevelTasks;
      }

      // Then go forward to find all child tasks
      const forwardChain: FarmingPlanTask[] = [];
      const processedChildIds = new Set<string>();
      currentLevelTasks = [startTask];

      while (currentLevelTasks.length > 0) {
        const nextLevelTasks: FarmingPlanTask[] = [];

        for (const task of currentLevelTasks) {
          const childTasks = await this.taskItemTransferService.getChildTasks(user, task.name);

          for (const childTask of childTasks) {
            if (!processedChildIds.has(childTask.name)) {
              forwardChain.push(childTask);
              processedChildIds.add(childTask.name);
              nextLevelTasks.push(childTask);
            }
          }
        }

        currentLevelTasks = nextLevelTasks;
      }

      // Legacy support - check for previous_task_id if no parent tasks found
      if (backwardChain.length === 0 && startTask.previous_task_id) {
        let currentTask = startTask;

        while (currentTask.previous_task_id) {
          const prevTask = await this.getTaskById(user, currentTask.previous_task_id);
          backwardChain.unshift(prevTask); // Add to the beginning of the array
          currentTask = prevTask;
        }
      }

      // Combine all tasks in the chain
      return [...backwardChain, startTask, ...forwardChain];
    } catch (error) {
      Logger.error(`Error getting task chain:`, error);
      throw error;
    }
  }

  /**
   * Updates a task's chain by linking it to a previous task
   * Updated to use the new TaskItemTransfer structure while maintaining
   * backward compatibility with the previous_task_id field
   * 
   * @param user Current user
   * @param taskId Task ID to update
   * @param previousTaskId Previous task ID to link to
   * @param itemIdToTransfer Optional item ID to transfer (if not specified, just links tasks)
   * @param quantity Optional quantity to transfer (default 0 if item is specified)
   * @returns Updated task
   */
  async updateTaskChain(
    user: ICurrentUser,
    taskId: string,
    previousTaskId: string,
    itemIdToTransfer?: string,
    quantity: number = 0
  ): Promise<FarmingPlanTask> {
    try {
      // Prevent task from linking to itself
      if (taskId === previousTaskId) {
        throw new HttpError(400, `Cannot link a task to itself`);
      }

      const task = await this.getTaskById(user, taskId);
      const previousTask = await this.getTaskById(user, previousTaskId);

      // Update the task's previous_task_id field for backward compatibility
      const repo = AppDataSource.getRepository(FarmingPlanTask);
      task.previous_task_id = previousTaskId;

      // Update task_chain_ids if present (for backward compatibility)
      if (previousTask.task_chain_ids) {
        try {
          const prevChainIds = JSON.parse(previousTask.task_chain_ids) as string[];
          const chainIds = [...prevChainIds, previousTaskId];
          task.task_chain_ids = JSON.stringify(chainIds);
        } catch (e) {
          Logger.error(`Error parsing previous task chain IDs:`, e);
          // If error parsing, just create a simple chain with the previous task
          task.task_chain_ids = JSON.stringify([previousTaskId]);
        }
      } else {
        // If previous task has no chain, start a new one with just the previous task
        task.task_chain_ids = JSON.stringify([previousTaskId]);
      }

      // Create a transfer if an item is specified
      if (itemIdToTransfer) {
        await this.taskItemTransferService.createItemTransfer(
          user,
          previousTaskId,
          taskId,
          itemIdToTransfer,
          quantity || 0
        );
      }

      // Save the updated task
      await repo.save(task);

      return task;
    } catch (error) {
      Logger.error(`Error updating task chain:`, error);
      throw error;
    }
  }

  /**
   * Validates if a task can perform a specific operation based on its type
   * @param user Current user
   * @param taskId Task ID
   * @param requiredType Required task type for the operation
   * @returns Boolean indicating if the operation is allowed
   */
  /**
   * Get task management info with filtering and pagination
   * This implementation mimics the old API using TypeORM
   */
  async getTaskManagementInfo(
    user: ICurrentUser,
    page: number = 1,
    size: number = 10,
    filters?: FilterTuple[],
    stateId?: string,
    templateId?: string,
    status?: string,
    assignedTo?: string,
    taskType?: string,
    orderBy?: string
  ) {
    try {
      const skip = (page - 1) * size;
      const take = size;

      // Create query builder with all necessary joins but select only required fields
      let qb = AppDataSource.getRepository(FarmingPlanTask)
        .createQueryBuilder('task')
        .leftJoin('task.farmingPlanState', 'state')
        .leftJoin('state.farmingPlan', 'farmingPlan')
        .leftJoin('farmingPlan.cropRelation', 'crop')
        .leftJoin('crop.zoneRelation', 'zone')
        .leftJoin('task.template', 'template')
        .leftJoin('task.assignedUser', 'assignedUser')
        .leftJoin('task.department', 'department')
        .leftJoin('task.environmentTemplate', 'environmentTemplate');

      // Select task fields
      qb = qb.select('task');

      // Select only required fields from related entities
      qb = qb.addSelect([
        'state.name', 'state.label',
        'farmingPlan.name',
        'crop.name',
        'crop.label',
        'zone.name', 'zone.customer_id',
        'template.name', 'template.label',
        'assignedUser.name', 'assignedUser.email', 'assignedUser.first_name', 'assignedUser.last_name',
        'department.name', 'department.label',
        'environmentTemplate.name', 'environmentTemplate.label', 'environmentTemplate.environment_code'
      ]);

      // Add VietPlants extension if applicable
      if (user.tenant?.name && isVietPlantsTenant(user.tenant.name)) {
        qb = qb.leftJoin('task.vietplantsExtension', 'vietplantsExtension')
          .addSelect('vietplantsExtension');
      }

      // Add task item transfers information with selective fields
      qb = qb.leftJoin('task.incomingTransfers', 'incomingTransfers')
        .leftJoin('incomingTransfers.sourceTask', 'parentTasks')
        .leftJoin('incomingTransfers.item', 'incomingItems')
        .leftJoin('task.outgoingTransfers', 'outgoingTransfers')
        .leftJoin('outgoingTransfers.targetTask', 'childTasks')
        .leftJoin('outgoingTransfers.item', 'outgoingItems')
        .addSelect([
          'incomingTransfers.name', 'incomingTransfers.source_task_id', 'incomingTransfers.item_id',
          'incomingTransfers.quantity', 'incomingTransfers.uom_id',
          'parentTasks.name',
          'incomingItems.name',
          'outgoingTransfers.name', 'outgoingTransfers.target_task_id',
          'childTasks.name',
          'outgoingItems.name'
        ]);

      // Base conditions
      qb = qb.where('task.deleted IS NULL')
        .andWhere('zone.customer_id = :customer_id', { customer_id: user.customer_id });

      // Apply filters
      if (stateId) {
        qb = qb.andWhere('task.farming_plan_state = :stateId', { stateId });
      }

      if (templateId) {
        qb = qb.andWhere('task.template_id = :templateId', { templateId });
      }

      if (status) {
        qb = qb.andWhere('task.status = :status', { status });
      }

      if (assignedTo) {
        qb = qb.andWhere('task.assigned_to = :assignedTo', { assignedTo });
      }

      if (taskType) {
        qb = qb.andWhere('task.task_type = :taskType', { taskType });
      }

      // Apply dynamic filters
      qb = applyQueryFilters(qb, filters, 'task');

      // Apply ordering
      if (orderBy) {
        const [field, direction] = orderBy.split(':');
        qb = qb.orderBy(`task.${field}`, direction.toUpperCase() as 'ASC' | 'DESC');
      } else {
        // Default ordering
        qb = qb.orderBy('task.creation', 'DESC');
      }

      // Apply pagination
      qb = qb.skip(skip).take(take);

      // Execute query
      const [tasks, total] = await qb.getManyAndCount();

      // Process tasks to include chain information and related production/warehouse items
      const processedTasks = await Promise.all(tasks.map(async (task) => {
        // Get parent and child task counts for the new multi-parent model
        const parentTaskCount = await AppDataSource.getRepository(TaskItemTransfer)
          .createQueryBuilder('transfer')
          .where('transfer.target_task_id = :taskId', { taskId: task.name })
          .getCount();

        const childTaskCount = await AppDataSource.getRepository(TaskItemTransfer)
          .createQueryBuilder('transfer')
          .where('transfer.source_task_id = :taskId', { taskId: task.name })
          .getCount();

        // Group item transfers by parent task
        const itemsByParent: Record<string, any[]> = {};
        if (task.incomingTransfers && task.incomingTransfers.length > 0) {
          task.incomingTransfers.forEach(transfer => {
            if (!itemsByParent[transfer.source_task_id]) {
              itemsByParent[transfer.source_task_id] = [];
            }
            itemsByParent[transfer.source_task_id].push({
              itemId: transfer.item_id,
              itemName: transfer.item?.name,
              quantity: transfer.quantity,
              uomId: transfer.uom_id
            });
          });
        }

        // Legacy chain support - include previous chain information if available
        let legacyChainTasks: FarmingPlanTask[] = [];
        if (task.previous_task_id) {
          try {
            legacyChainTasks = await this.getTaskChain(user, task.name);
            // Filter out the current task from the chain
            legacyChainTasks = legacyChainTasks.filter(t => t.name !== task.name);
          } catch (error) {
            Logger.error(`Error getting chain for task ${task.name}:`, error);
          }
        }

        // Get production quantities related to this task
        const productionQuantities = await AppDataSource.getRepository(ProductionQuantity)
          .createQueryBuilder('prodQty')
          .leftJoinAndSelect('prodQty.product', 'product')
          .leftJoinAndSelect('prodQty.activeUOM', 'activeUOM')
          .where('prodQty.task_id = :taskId', { taskId: task.name })
          .andWhere('prodQty.deleted IS NULL')
          .getMany();

        // Get warehouse items used in this task
        const warehouseItemsUsed = await AppDataSource.getRepository(WarehouseItemTaskUsed)
          .createQueryBuilder('warehouseItem')
          .leftJoinAndSelect('warehouseItem.iotCategory', 'iotCategory')
          .leftJoinAndSelect('warehouseItem.activeUOM', 'activeUOM')
          .where('warehouseItem.task_id = :taskId', { taskId: task.name })
          .andWhere('warehouseItem.deleted IS NULL')
          .getMany();

        // Return enhanced task with chaining information and related items
        return {
          ...task,
          taskChainInfo: {
            parentTaskCount,
            childTaskCount,
            itemsByParent,
            hasLegacyChain: !!task.previous_task_id || !!task.task_chain_ids,
            legacyChainTasks: legacyChainTasks
          },
          productionInfo: {
            items: productionQuantities.map(pq => ({
              name: pq.name,
              product_id: pq.product_id,
              product_name: pq.product?.name,
              product_label: pq.product?.label,
              quantity: pq.quantity,
              exp_quantity: pq.exp_quantity,
              finished_quantity: pq.finished_quantity,
              lost_quantity: pq.lost_quantity,
              draft_quantity: pq.draft_quantity,
              issued_quantity: pq.issued_quantity,
              total_qty_in_crop: pq.total_qty_in_crop,
              active_uom: pq.active_uom,
              uom_name: pq.activeUOM?.name,
              active_conversion_factor: pq.active_conversion_factor,
              description: pq.description
            })),
            total_items: productionQuantities.length
          },
          warehouseItemsInfo: {
            items: warehouseItemsUsed.map(wi => ({
              name: wi.name,
              iot_category_id: wi.iot_category_id,
              category_name: wi.iotCategory?.name,
              category_label: wi.iotCategory?.label,
              quantity: wi.quantity,
              exp_quantity: wi.exp_quantity,
              loss_quantity: wi.loss_quantity,
              issued_quantity: wi.issued_quantity,
              draft_quantity: wi.draft_quantity,
              total_qty_in_crop: wi.total_qty_in_crop,
              active_uom: wi.active_uom,
              uom_name: wi.activeUOM?.name,
              active_conversion_factor: wi.active_conversion_factor,
              description: wi.description
            })),
            total_items: warehouseItemsUsed.length
          }
        };
      }));

      return {
        data: processedTasks,
        page,
        size,
        total
      };
    } catch (error) {
      Logger.error('Error getting task management info:', error);
      throw error;
    }
  }

  /**
   * Update a task with partial data
   * Any field included in the request body will be updated
   * @param user Current user
   * @param taskId Task ID to update
   * @param updateData Partial data to update
   * @returns Updated task
   */
  async updateTask(user: ICurrentUser, taskId: string, updateData: any): Promise<FarmingPlanTask> {
    try {
      // Get the existing task
      const task = await this.getTaskById(user, taskId);

      // Prevent task from linking to itself
      if (updateData.previous_task_id === taskId) {
        throw new HttpError(400, `Cannot link a task to itself`);
      }

      // Store previous task ID before update
      const oldPreviousTaskId = task.previous_task_id;

      // Handle chain linking if previous_task_id has changed
      if (updateData.previous_task_id !== undefined && updateData.previous_task_id !== oldPreviousTaskId) {
        // Double-check to prevent self-reference
        if (updateData.previous_task_id === taskId) {
          throw new HttpError(400, `Cannot link a task to itself`);
        }

        // Create a clean copy of update data without chain-related fields
        const cleanUpdateData = { ...updateData };
        delete cleanUpdateData.previous_task_id;
        delete cleanUpdateData.task_chain_ids;

        // Remove all relation fields and computed properties
        this.cleanUpdateDataObject(cleanUpdateData);

        // Update the basic task data first
        const repo = AppDataSource.getRepository(FarmingPlanTask);

        if (Object.keys(cleanUpdateData).length > 0) {
          await repo.createQueryBuilder()
            .update(FarmingPlanTask)
            .set(cleanUpdateData)
            .where("name = :taskId", { taskId })
            .execute();
        }

        if (updateData.previous_task_id && updateData.previous_task_id.trim() !== '') {
          // Link to a new previous task
          try {
            // Update the chain separately using the dedicated method
            return await this.updateTaskChain(user, taskId, updateData.previous_task_id);
          } catch (chainError) {
            Logger.error(`Error updating task chain:`, chainError);
            throw chainError; // Re-throw to prevent invalid state
          }
        } else {
          // Remove from chain (previous_task_id set to empty string)
          // Use direct SQL update to avoid any TypeORM relation issues
          await repo.createQueryBuilder()
            .update(FarmingPlanTask)
            .set({
              previous_task_id: '',
              task_chain_ids: ''
            })
            .where("name = :taskId", { taskId })
            .execute();

          // Fetch the updated task with all relations
          return await this.getTaskById(user, taskId);
        }
      }

      // Save the updated task using direct SQL update
      // We need to exclude relation fields that can't be updated via QueryBuilder
      const repo = AppDataSource.getRepository(FarmingPlanTask);

      // Create a clean copy without relation fields and computed properties
      const cleanUpdateData = { ...updateData };
      this.cleanUpdateDataObject(cleanUpdateData);

      // Only update if there are fields to update
      if (Object.keys(cleanUpdateData).length > 0) {
        await repo.createQueryBuilder()
          .update(FarmingPlanTask)
          .set(cleanUpdateData)
          .where("name = :taskId", { taskId })
          .execute();
      }

      // Reload the task with all relations
      return await this.getTaskById(user, taskId);
    } catch (error) {
      Logger.error(`Error updating task:`, error);
      throw error;
    }
  }

  /**
   * Helper method to clean update data object by removing relation fields and computed properties
   * that can't be directly updated through TypeORM's QueryBuilder
   * @param updateData Object to clean
   */
  private cleanUpdateDataObject(updateData: any): void {
    if (!updateData) return;

    // Remove relation fields that can't be updated via QueryBuilder
    delete updateData.nextTasks;
    delete updateData.previousTask;
    delete updateData.farmingPlanState;
    delete updateData.assignedUser;
    delete updateData.department;
    delete updateData.environmentTemplate;
    delete updateData.template;
    delete updateData.vietplantsExtension;

    // Remove the newly added transfer relations
    delete updateData.incomingTransfers;
    delete updateData.outgoingTransfers;

    // Remove computed properties added by our API
    delete updateData.taskChainInfo;
    delete updateData.productionInfo;
    delete updateData.warehouseItemsInfo;
  }

  async validateTaskTypeOperation(user: ICurrentUser, taskId: string, requiredType: string): Promise<boolean> {
    try {
      const task = await this.getTaskById(user, taskId);

      if (!task.task_type) {
        throw new HttpError(400, `Task ${taskId} does not have a defined type`);
      }

      if (task.task_type !== requiredType) {
        throw new HttpError(403, `Operation not allowed for task type ${task.task_type}. Required type: ${requiredType}`);
      }

      return true;
    } catch (error) {
      Logger.error(`Error validating task type operation:`, error);
      throw error;
    }
  }

  async assignTasks(user: ICurrentUser, dtoArray: AssignFarmingPlanTaskDtoArray): Promise<any[]> {
    try {
      const { state_id, department_id, tasks, production_plan_id } = dtoArray;

      // STEP 1: Pre-allocation validation - Check allocation availability BEFORE creating tasks
      Logger.debug(`Starting pre-allocation validation for ${tasks.length} tasks`);
      const allocationValidation = await this.validateAllocationAvailability(user, tasks, production_plan_id);

      if (!allocationValidation.isValid) {
        Logger.error(`Pre-allocation validation failed: ${allocationValidation.error}`);

        // Create user-friendly error message in Vietnamese
        let errorMessage = '';

        if (allocationValidation.details) {
          const details = allocationValidation.details;

          if (details.yieldOutputName) {
            // Case: Insufficient remaining quantity
            errorMessage = `❌ Không thể phân công công việc do không đủ số lượng sản phẩm dự kiến.`;
            errorMessage += `\n\n📊 Thông tin chi tiết:`;
            errorMessage += `\n• Số lượng cần thiết: ${details.requiredQuantity.toLocaleString()} đơn vị`;
            errorMessage += `\n• Số lượng còn lại hiện tại: ${details.availableQuantity.toLocaleString()} đơn vị`;

            // Show information about existing tasks if available
            if (details.freedFromExistingTasks !== undefined && details.freedFromExistingTasks > 0) {
              errorMessage += `\n• Số lượng sẽ được giải phóng từ công việc cũ: ${details.freedFromExistingTasks.toLocaleString()} đơn vị`;
              errorMessage += `\n• Tổng số lượng có thể sử dụng: ${details.effectiveAvailableQuantity.toLocaleString()} đơn vị`;
            }

            errorMessage += `\n• Thiếu hụt: ${details.shortfall.toLocaleString()} đơn vị`;

            if (details.dateRange) {
              // Format date range for Vietnamese users
              const [startDate, endDate] = details.dateRange.split(' to ');
              const formattedStartDate = moment(startDate).format('DD/MM/YYYY');
              const formattedEndDate = moment(endDate).format('DD/MM/YYYY');
              errorMessage += `\n• Thời gian: ${formattedStartDate} - ${formattedEndDate}`;
            }

            if (details.affectedTasks && details.affectedTasks.length > 0) {
              errorMessage += `\n\n📋 Công việc bị ảnh hưởng:`;
              for (const task of details.affectedTasks) {
                // Use template label for better readability
                const templateName = task.template_label || task.template_id;
                const taskType = task.isExisting ? '(Cập nhật)' : '(Mới)';
                let taskInfo = `\n• ${templateName} ${taskType} - Số lượng: ${task.quantity.toLocaleString()} đơn vị`;

                // Show current quantity for existing tasks
                if (task.isExisting && task.currentQuantity !== undefined) {
                  taskInfo += ` (hiện tại: ${task.currentQuantity.toLocaleString()} đơn vị)`;
                }

                errorMessage += taskInfo;
              }
            }

            errorMessage += `\n\n💡 Gợi ý giải quyết:`;
            errorMessage += `\n• Giảm số lượng dự kiến của các công việc`;
            errorMessage += `\n• Chọn kế hoạch sản xuất khác có đủ số lượng`;
            errorMessage += `\n• Cập nhật số lượng sản phẩm dự kiến trong kế hoạch sản xuất`;
          } else {
            // Case: No yield output found
            errorMessage = `❌ Không tìm thấy sản phẩm dự kiến phù hợp cho công việc này.`;
            errorMessage += `\n\n📊 Thông tin chi tiết:`;
            if (details.environmentTemplateId) {
              errorMessage += `\n• Mẫu môi trường: ${details.environmentTemplateId}`;
            }
            if (details.dateRange) {
              // Format date range for Vietnamese users
              const [startDate, endDate] = details.dateRange.split(' to ');
              const formattedStartDate = moment(startDate).format('DD/MM/YYYY');
              const formattedEndDate = moment(endDate).format('DD/MM/YYYY');
              errorMessage += `\n• Thời gian: ${formattedStartDate} - ${formattedEndDate}`;
            }
            if (details.productionPlanId) {
              errorMessage += `\n• Kế hoạch sản xuất: ${details.productionPlanId}`;
            }

            errorMessage += `\n\n💡 Gợi ý giải quyết:`;
            errorMessage += `\n• Kiểm tra kế hoạch sản xuất có chứa sản phẩm dự kiến phù hợp`;
            errorMessage += `\n• Đảm bảo thời gian công việc nằm trong khoảng thời gian của sản phẩm dự kiến`;
            errorMessage += `\n• Chọn mẫu công việc phù hợp với môi trường sản xuất`;
          }
        } else {
          // Fallback error message
          errorMessage = `❌ Không thể phân công công việc do vấn đề về phân bổ sản phẩm. Vui lòng kiểm tra lại thông tin và thử lại.`;
        }

        throw new HttpError(400, errorMessage);
      }

      Logger.debug(`Pre-allocation validation passed - proceeding with task creation`);

      const tasksToProcess: any[] = [];
      const repo = AppDataSource.getRepository(FarmingPlanTaskTemplate);

      for (const dto of tasks as AssignFarmingPlanTaskDto[]) {
        // Validate start_date and end_date
        const start = moment(dto.start_date);
        const end = moment(dto.end_date);
        if (end.isBefore(start)) {
          throw new HttpError(400, `end_date must be on or after start_date for task with template_id ${dto.template_id}`);
        }

        // If task has a name, validate it exists for update operations
        if (dto.name) {
          const existingTask = await AppDataSource.getRepository(FarmingPlanTask).findOne({
            where: { name: dto.name }
          });
          if (!existingTask) {
            throw new HttpError(404, `Task with name ${dto.name} not found for update`);
          }
          // Verify the task belongs to the current customer
          if (user.customer?.id && existingTask.owner !== user.customer.id.toString()) {
            throw new HttpError(403, `Unauthorized access to task ${dto.name}`);
          }
        }

        // Fetch task template
        const template = await repo.findOne({
          where: { name: dto.template_id },
          relations: ['itemTaskTemplates', 'itemTaskTemplates.item', 'productionTaskTemplates', 'productionTaskTemplates.item'],
        });
        if (!template) {
          throw new HttpError(404, `Template ${dto.template_id} not found`);
        }

        // Strict validation for task_type field - fail-fast if missing
        if (!template.task_type || template.task_type.trim() === '') {
          throw new HttpError(400, `Mẫu công việc "${dto.template_id}" không có loại công việc (task_type) được định nghĩa. Vui lòng cập nhật mẫu công việc với loại công việc hợp lệ trước khi phân công.`);
        }

        // Map itemTaskTemplates to item_list, using exp_quantity from DTO
        const itemList = template.itemTaskTemplates.map((it: ItemTaskTemplate) => ({
          name: it.item?.name ?? it.item_id,
          quantity: 0,
          exp_quantity: it.quantity,
          loss_quantity: 0,
          description: it.description,
          iot_category_id: it.item_id,
          active_uom: it.uom_id,
          active_conversion_factor: 1,
        } as WarehouseItemTaskUsedDto));

        // Map productionTaskTemplates to prod_quantity_list
        const prodQuantityList = template.productionTaskTemplates.map((pt: ProductionTaskTemplate) => ({
          name: pt.item?.name ?? pt.item_id,
          quantity: 0,
          exp_quantity: dto.exp_quantity,
          description: pt.description,
          product_id: pt.item_id,
          active_uom: pt.uom_id,
          active_conversion_factor: 1,
        } as ProductionQuantityDto));

        // Create/update task based on the DTO
        const taskData: CreateFarmingPlanTaskDto = {
          label: template.label,
          image: template.image,
          start_date: dto.start_date,
          end_date: dto.end_date,
          status: dto.status || 'Plan',
          department_id: department_id,
          priority_level: template.priority_level,
          farming_plan_state: state_id,
          description: dto.description,
          assigned_to: dto.assigned_to,
          involve_in_users: [],
          template_id: dto.template_id,
          environment_template_id: template.environment_template_id,
          task_type: template.task_type!, // Strict assignment - validation ensures this is always defined
          worksheet_list: [],
          item_list: itemList,
          prod_quantity_list: prodQuantityList,
          todo_list: [],
          task_progress: dto.name ? 0 : 0, // Hiện tại field này k dùng, nên set tạm = 0 để tránh lỗi
        };

        // Only include 'name' field if it exists (for update operations)
        if (dto.name) {
          taskData.name = dto.name;
        }

        // Only add non-undefined fields to avoid overwriting with undefined values
        const cleanTaskData = Object.fromEntries(
          Object.entries(taskData).filter(([_, value]) => value !== undefined)
        );

        tasksToProcess.push(cleanTaskData);
      }

      if (!tasksToProcess.length) {
        return [];
      }

      // Determine if we need upsert or just insert based on whether any tasks have 'name' field
      const hasExistingTasks = tasksToProcess.some(task => task.name);

      console.log(hasExistingTasks ? "start to upsert tasks" : "start to create tasks");
      const result = hasExistingTasks
        ? await this.oldTaskService.upsertTaskandResourceArray(
          user,
          { tasks: tasksToProcess } as FarmingPlanTaskArray,
          0,
        )
        : await this.oldTaskService.createTaskandResourceArray(
          user,
          { tasks: tasksToProcess } as FarmingPlanTaskArray,
          0,
        );
      // console.log("result", result);
      // Logger.debug(`Raw result from task service: ${JSON.stringify(result)}`);

      // Fix: Handle the flat array structure correctly
      // The result is a flat array containing all records (tasks, items, production quantities, etc.)
      // We need to filter for task records only (those with farming_plan_state field)
      const allRecords = Array.isArray(result) ? result : [];
      // Logger.debug(allRecords)
      Logger.debug(`Total records returned: ${allRecords.length}`);

      // Filter for task records only (those with farming_plan_state field)
      const taskRecords = allRecords.filter((record: any) =>
        record.farming_plan_state !== undefined && record.farming_plan_state !== null
      );
      Logger.debug(`Task records found: ${taskRecords.length}`);

      // Filter for tasks belonging to this specific state
      const processedTasks: any = taskRecords.filter((task: any) => task.farming_plan_state === state_id);
      Logger.debug(`Processed tasks for state ${state_id}: ${processedTasks.length}`);

      //for each task, create label print request (only for new tasks, not updates)
      for (const task of processedTasks) {
        if (hasExistingTasks) {
          // Only create label requests for newly created tasks (those without original name in DTO)
          const originalDto = tasks.find(dto => dto.name === task.name);
          if (!originalDto?.name) { // This was a new task creation
            await this.labelPrintManagementService.autoCreateLabelRequests(task.name, user.user_id, user.customer_id);
          }
        } else {
          // All tasks are new when using createTaskandResourceArray
          await this.labelPrintManagementService.autoCreateLabelRequests(task.name, user.user_id, user.customer_id);
        }
      }

      console.log(hasExistingTasks ? "success upsert tasks" : "success create tasks");

      // NEW: Sync allocations with tasks after successful task creation/update
      Logger.debug(`About to sync allocations - processedTasks: ${processedTasks.length}, originalDTOs: ${tasks.length}`);
      Logger.debug(`ProcessedTasks sample: ${processedTasks.slice(0, 2).map((t: any) => `{name: ${t.name}, assigned_to: ${t.assigned_to}}`).join(', ')}`);
      Logger.debug(`OriginalDTOs sample: ${tasks.slice(0, 2).map((dto: any) => `{name: ${dto.name || 'undefined'}, assigned_to: ${dto.assigned_to}}`).join(', ')}`);

      try {
        await this.syncTaskAllocations(user, processedTasks, tasks, production_plan_id);
      } catch (allocationError) {
        Logger.warn('Task assignment succeeded but allocation sync failed:', allocationError);
        // Don't fail the entire operation if allocation sync fails
        // This ensures backward compatibility and prevents task creation failures
      }

      return Array.isArray(result) ? result : [];
    } catch (error) {
      Logger.error(`Error assigning tasks:`, error);
      throw error;
    }
  }

  /**
   * Synchronize allocations with task assignments
   * This method links tasks to production plan allocations to solve the cumulative allocation issue
   *
   * IMPORTANT: Only tasks with task_type = "Subculture" will create/update allocations.
   * Other task types (Harvest, Planting, Other, etc.) will skip the allocation process entirely.
   */
  private async syncTaskAllocations(
    user: ICurrentUser,
    processedTasks: FarmingPlanTask[],
    originalTaskDtos: AssignFarmingPlanTaskDto[],
    productionPlanId?: string
  ): Promise<void> {
    Logger.debug(`Starting syncTaskAllocations for user ${user.user_id} with ${processedTasks.length} processed tasks and ${originalTaskDtos.length} original DTOs, production_plan_id: ${productionPlanId || 'not specified'}`);

    // Enhanced debugging: Show detailed information about both arrays
    Logger.debug(`Processed tasks details: ${processedTasks.map(task => `{name: ${task.name}, assigned_to: ${task.assigned_to}, template_id: ${task.template_id}, environment_template_id: ${task.environment_template_id}}`).join(', ')}`);
    Logger.debug(`Original DTOs details: ${originalTaskDtos.map(dto => `{name: ${dto.name || 'undefined'}, assigned_to: ${dto.assigned_to}, template_id: ${dto.template_id}}`).join(', ')}`);

    try {
      // Enhanced logic: Process all tasks that need allocation sync
      // This includes both newly created tasks and updated existing tasks
      for (let i = 0; i < processedTasks.length; i++) {
        const processedTask = processedTasks[i];
        Logger.debug(`Processing task ${i + 1}/${processedTasks.length}: ${processedTask.name || 'unnamed'}`);
        Logger.debug(`Task details: assigned_to=${processedTask.assigned_to}, template_id=${processedTask.template_id}, environment_template_id=${processedTask.environment_template_id}`);

        // Convert dates to string format for database queries
        // Use local timezone to preserve the original date, not UTC conversion
        const startDateStr = moment(processedTask.start_date).format('YYYY-MM-DD');
        const endDateStr = moment(processedTask.end_date).format('YYYY-MM-DD');

        Logger.debug(`Task dates: start_date=${startDateStr}, end_date=${endDateStr}`);

        // Enhanced DTO matching logic for both new and existing tasks
        let originalDto = originalTaskDtos.find(dto => {
          // Case 1: Exact match by name (for existing tasks being updated)
          if (dto.name && processedTask.name && dto.name === processedTask.name) {
            Logger.debug(`Found exact name match: DTO.name=${dto.name} === task.name=${processedTask.name}`);
            return true;
          }

          // Case 2: Match by assigned_to and template_id (for newly created tasks)
          if (!dto.name && processedTask.assigned_to === dto.assigned_to && processedTask.template_id === dto.template_id) {
            Logger.debug(`Found new task match: assigned_to=${dto.assigned_to}, template_id=${dto.template_id}`);
            return true;
          }

          return false;
        });

        // If no match found, try a more flexible approach for newly created tasks
        if (!originalDto && originalTaskDtos.length === 1 && !originalTaskDtos[0].name) {
          // If there's only one original DTO without a name, it's likely a new task creation
          originalDto = originalTaskDtos[0];
          Logger.debug(`Using single unnamed DTO for new task: ${processedTask.name}`);
        }

        if (!originalDto) {
          Logger.debug(`No matching original DTO found for task ${processedTask.name || 'unnamed'} - skipping allocation sync`);
          Logger.debug(`Task search criteria: name=${processedTask.name}, assigned_to=${processedTask.assigned_to}, template_id=${processedTask.template_id}`);
          Logger.debug(`Available original DTOs: ${originalTaskDtos.map(dto => `{name: ${dto.name || 'undefined'}, assigned_to: ${dto.assigned_to}, template_id: ${dto.template_id}}`).join(', ')}`);
          continue;
        }

        Logger.debug(`Found matching original DTO for task ${processedTask.name}: ${JSON.stringify({
          name: originalDto.name || 'undefined',
          assigned_to: originalDto.assigned_to,
          template_id: originalDto.template_id,
          department_id: originalDto.department_id
        })}`);

        // Validate required fields for allocation sync
        if (!processedTask.environment_template_id) {
          Logger.warn(`Skipping allocation sync for task ${processedTask.name} - missing environment_template_id`);
          continue;
        }

        // Filter tasks by task_type - only process "Subculture" tasks for allocation
        if (processedTask.task_type !== 'Subculture') {
          Logger.debug(`Skipping allocation sync for task ${processedTask.name} - task_type is '${processedTask.task_type}', only 'Subculture' tasks are processed for allocation`);
          continue;
        }

        Logger.debug(`Task ${processedTask.name} has task_type 'Subculture' - proceeding with allocation sync`);

        // Use production plan ID from the array-level parameter for scoped allocation
        Logger.debug(`Production plan scope: ${productionPlanId ? `scoped to ${productionPlanId}` : 'all plans'}`);

        // Validate production plan if specified
        if (productionPlanId) {
          const validation = await this.validateProductionPlan(user, productionPlanId);
          if (!validation.isValid) {
            Logger.warn(`Skipping allocation sync for task ${processedTask.name} - ${validation.error}`);
            continue;
          }
          Logger.debug(`Production plan validated: ${validation.plan?.name} (${validation.plan?.label})`);
        }

        // Find matching yield output based on environment template and date range
        // If productionPlanId is provided, scope the search to that specific production plan
        Logger.debug(`Searching for yield output with environment_template_id=${processedTask.environment_template_id}, start_date=${startDateStr}, end_date=${endDateStr}, production_plan=${productionPlanId || 'any'}`);

        const yieldOutput = await this.findMatchingYieldOutput(
          user,
          processedTask.environment_template_id!,
          startDateStr,
          endDateStr,
          productionPlanId // Pass the production plan ID for scoped search
        );

        if (!yieldOutput) {
          const planScope = productionPlanId ? ` in production plan ${productionPlanId}` : ' across all production plans';
          Logger.warn(`No matching yield output found for task ${processedTask.name} with environment_template_id=${processedTask.environment_template_id}, date range: ${startDateStr} to ${endDateStr}${planScope}`);
          continue;
        }

        Logger.debug(`Found matching yield output: ${yieldOutput.name}`);

        // Get expected quantity from production quantities
        Logger.debug(`Getting expected quantity for task ${processedTask.name}`);
        const expectedQuantity = await this.getTaskExpectedQuantity(processedTask.name);
        Logger.debug(`Expected quantity for task ${processedTask.name}: ${expectedQuantity}`);

        if (expectedQuantity > 0) {
          Logger.debug(`Syncing allocation: yield_output=${yieldOutput.name}, assigned_to=${processedTask.assigned_to}, task=${processedTask.name}, quantity=${expectedQuantity}`);

          // Sync allocation with task
          await this.allocationService.syncAllocationWithTask(
            yieldOutput.name,
            processedTask.assigned_to!,
            processedTask.name,
            expectedQuantity,
            user,
            `Auto-synced allocation for task ${processedTask.name}`
          );

          Logger.info(`Successfully synced allocation for task ${processedTask.name}: ${expectedQuantity} units to yield output ${yieldOutput.name}`);
        } else {
          Logger.debug(`Skipping allocation sync for task ${processedTask.name} - expected quantity is ${expectedQuantity}`);
        }
      }

      Logger.debug(`Completed syncTaskAllocations successfully for ${processedTasks.length} tasks`);
    } catch (error) {
      Logger.error('Error syncing task allocations:', error);
      if (error instanceof Error) {
        Logger.error(`Error details: ${error.message}`);
        Logger.error(`Stack trace: ${error.stack}`);
      }
      throw error;
    }
  }

  /**
   * Find matching production plan yield output for a task
   * @param user Current user for authorization
   * @param environmentTemplateId Environment template to match
   * @param startDate Task start date
   * @param endDate Task end date
   * @param productionPlanId Optional production plan ID to scope the search
   */
  private async findMatchingYieldOutput(
    user: ICurrentUser,
    environmentTemplateId: string,
    startDate: string,
    endDate: string,
    productionPlanId?: string
  ): Promise<ProductionPlanYieldExpectedOutput | null> {
    try {
      Logger.debug(`Finding yield output: environmentTemplateId=${environmentTemplateId}, productionPlanId=${productionPlanId || 'any'}, dateRange=${startDate} to ${endDate}`);

      // Build query for yield output that matches environment template and overlaps with task date range
      // Date overlap logic: Two date ranges [A1,A2] and [B1,B2] overlap if A1 <= B2 AND A2 >= B1
      // Where: yieldOutput = [start_date, end_date], task = [startDate, endDate]
      // So: yieldOutput.start_date <= task.endDate AND yieldOutput.end_date >= task.startDate
      let queryBuilder = this.yieldExpectedOutputRepository
        .createQueryBuilder('yieldOutput')
        .leftJoin('yieldOutput.productionPlan', 'productionPlan')
        .where('yieldOutput.environment_template_id = :environmentTemplateId', { environmentTemplateId })
        .andWhere('productionPlan.customer_id = :customerId', { customerId: user.customer_id })
        .andWhere('yieldOutput.start_date <= :endDate', { endDate })
        .andWhere('yieldOutput.end_date >= :startDate', { startDate });

      const yieldOutputTest = await queryBuilder
        .orderBy('yieldOutput.start_date', 'ASC')
        .getOne();
      if (yieldOutputTest) {
        Logger.debug(`Found yield output test: ${yieldOutputTest.name} from production plan: ${yieldOutputTest.production_plan_id}`);
      } else {
        Logger.debug(`No yield output test found with the specified criteria`);
      }

      // Add production plan filter if specified
      if (productionPlanId) {
        Logger.debug(`Adding production plan filter: ${productionPlanId}`);
        queryBuilder = queryBuilder.andWhere('yieldOutput.production_plan_id = :productionPlanId', { productionPlanId });
      }

      const yieldOutput = await queryBuilder
        .orderBy('yieldOutput.start_date', 'ASC')
        .getOne();

      if (yieldOutput) {
        Logger.debug(`Found yield output: ${yieldOutput.name} from production plan: ${yieldOutput.production_plan_id}`);
      } else {
        Logger.debug(`No yield output found with the specified criteria`);
      }

      return yieldOutput;
    } catch (error) {
      Logger.error('Error finding matching yield output:', error);
      return null;
    }
  }

  /**
   * Get expected quantity for a task from its production quantities
   */
  private async getTaskExpectedQuantity(taskId: string): Promise<number> {
    try {
      const productionQuantities = await AppDataSource.getRepository(ProductionQuantity)
        .createQueryBuilder('prodQty')
        .where('prodQty.task_id = :taskId', { taskId })
        .andWhere('prodQty.deleted IS NULL')
        .getMany();

      // Sum up all expected quantities
      return productionQuantities.reduce((total, pq) => total + (pq.exp_quantity || 0), 0);
    } catch (error) {
      Logger.error(`Error getting expected quantity for task ${taskId}:`, error);
      return 0;
    }
  }

  /**
   * Validate that the specified production plan exists and belongs to the user's customer
   */
  private async validateProductionPlan(
    user: ICurrentUser,
    productionPlanId: string
  ): Promise<{ isValid: boolean; plan?: any; error?: string }> {
    try {
      Logger.debug(`Validating production plan: ${productionPlanId} for customer: ${user.customer_id}`);

      const plan = await this.productionPlanRepository.findOne({
        where: {
          name: productionPlanId,
          customer_id: user.customer_id
        }
      });

      if (!plan) {
        const error = `Production plan ${productionPlanId} not found or not accessible for customer ${user.customer_id}`;
        Logger.warn(error);
        return { isValid: false, error };
      }

      Logger.debug(`Production plan validated: ${plan.name} (${plan.label})`);
      return { isValid: true, plan };
    } catch (error) {
      Logger.error(`Error validating production plan ${productionPlanId}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { isValid: false, error: `Validation error: ${errorMessage}` };
    }
  }

  /**
   * Pre-allocation validation to check if there's sufficient remaining quantity
   * before creating or updating tasks
   *
   * IMPORTANT: Only tasks with task_type = "Subculture" will be validated for allocation.
   * Other task types (Harvest, Planting, Other, etc.) will skip validation entirely.
   */
  private async validateAllocationAvailability(
    user: ICurrentUser,
    tasks: AssignFarmingPlanTaskDto[],
    productionPlanId?: string
  ): Promise<{ isValid: boolean; error?: string; details?: any }> {
    try {
      Logger.debug(`Starting pre-allocation validation for ${tasks.length} tasks, production_plan_id: ${productionPlanId || 'not specified'}`);

      // Group tasks by environment template and date range for validation
      const taskGroups = new Map<string, {
        environmentTemplateId: string;
        startDate: string;
        endDate: string;
        totalQuantity: number;
        existingTasksQuantity: number; // Track quantity from existing tasks that will be freed up
        tasks: Array<{ dto: AssignFarmingPlanTaskDto; quantity: number; template: any; isExisting: boolean; currentQuantity?: number }>;
      }>();

      // Process each task and group by environment template
      for (const taskDto of tasks) {
        // Get template to find environment_template_id and template info
        const template = await AppDataSource.getRepository(FarmingPlanTaskTemplate).findOne({
          where: { name: taskDto.template_id }
        });

        if (!template?.environment_template_id) {
          Logger.warn(`Skipping allocation validation for task with template_id ${taskDto.template_id} - no environment_template_id found`);
          continue;
        }

        // Filter tasks by task_type - only validate allocation for "Subculture" tasks
        if (template.task_type !== 'Subculture') {
          Logger.debug(`Skipping allocation validation for task with template_id ${taskDto.template_id} - task_type is '${template.task_type}', only 'Subculture' tasks require allocation validation`);
          continue;
        }

        Logger.debug(`Task with template_id ${taskDto.template_id} has task_type 'Subculture' - proceeding with allocation validation`);

        // Convert dates to string format
        // Use local timezone to preserve the original date, not UTC conversion
        const startDateStr = moment(taskDto.start_date).format('YYYY-MM-DD');
        const endDateStr = moment(taskDto.end_date).format('YYYY-MM-DD');

        // Create a unique key for grouping
        const groupKey = `${template.environment_template_id}_${startDateStr}_${endDateStr}`;

        if (!taskGroups.has(groupKey)) {
          taskGroups.set(groupKey, {
            environmentTemplateId: template.environment_template_id,
            startDate: startDateStr,
            endDate: endDateStr,
            totalQuantity: 0,
            existingTasksQuantity: 0,
            tasks: []
          });
        }

        const group = taskGroups.get(groupKey)!;

        // Check if this is an existing task (has name property)
        const isExisting = !!taskDto.name;
        let currentQuantity = 0;

        if (isExisting) {
          // Get current quantity of existing task for proper calculation
          try {
            currentQuantity = await this.getTaskExpectedQuantity(taskDto.name!);
            group.existingTasksQuantity += currentQuantity;
            Logger.debug(`Existing task ${taskDto.name} current quantity: ${currentQuantity}, new quantity: ${taskDto.exp_quantity}`);
          } catch (error) {
            Logger.warn(`Could not get current quantity for existing task ${taskDto.name}, assuming 0`);
          }
        }

        group.totalQuantity += taskDto.exp_quantity;
        group.tasks.push({
          dto: taskDto,
          quantity: taskDto.exp_quantity,
          template,
          isExisting,
          currentQuantity
        });
      }

      Logger.debug(`Grouped tasks into ${taskGroups.size} allocation groups for validation`);

      // Validate each group against available yield outputs
      for (const [groupKey, group] of taskGroups) {
        Logger.debug(`Validating group ${groupKey}: ${group.totalQuantity} units needed, ${group.existingTasksQuantity} units will be freed from existing tasks`);

        // Find matching yield output for this group
        const yieldOutput = await this.findMatchingYieldOutput(
          user,
          group.environmentTemplateId,
          group.startDate,
          group.endDate,
          productionPlanId
        );

        if (!yieldOutput) {
          const planScope = productionPlanId ? ` in production plan ${productionPlanId}` : ' across all production plans';
          const error = `No yield output found for environment template ${group.environmentTemplateId} with date range ${group.startDate} to ${group.endDate}${planScope}`;
          Logger.error(error);
          return {
            isValid: false,
            error,
            details: {
              environmentTemplateId: group.environmentTemplateId,
              dateRange: `${group.startDate} to ${group.endDate}`,
              productionPlanId,
              requiredQuantity: group.totalQuantity,
              availableQuantity: 0,
              affectedTasks: group.tasks.map(t => ({
                template_id: t.dto.template_id,
                template_label: t.template?.label || t.dto.template_id,
                assigned_to: t.dto.assigned_to,
                quantity: t.quantity,
                isExisting: t.isExisting,
                currentQuantity: t.currentQuantity
              }))
            }
          };
        }

        // Calculate effective available quantity: current remaining + quantity freed from existing tasks
        const effectiveAvailableQuantity = yieldOutput.remaining_quantity + group.existingTasksQuantity;

        // Check if there's sufficient effective remaining quantity
        if (effectiveAvailableQuantity < group.totalQuantity) {
          const error = `Insufficient remaining quantity in yield output ${yieldOutput.name}. Required: ${group.totalQuantity}, Available: ${yieldOutput.remaining_quantity}, Freed from existing tasks: ${group.existingTasksQuantity}, Effective available: ${effectiveAvailableQuantity}`;
          Logger.error(error);
          return {
            isValid: false,
            error,
            details: {
              yieldOutputName: yieldOutput.name,
              environmentTemplateId: group.environmentTemplateId,
              dateRange: `${group.startDate} to ${group.endDate}`,
              productionPlanId: yieldOutput.production_plan_id,
              requiredQuantity: group.totalQuantity,
              availableQuantity: yieldOutput.remaining_quantity,
              freedFromExistingTasks: group.existingTasksQuantity,
              effectiveAvailableQuantity,
              shortfall: group.totalQuantity - effectiveAvailableQuantity,
              affectedTasks: group.tasks.map(t => ({
                template_id: t.dto.template_id,
                template_label: t.template?.label || t.dto.template_id,
                assigned_to: t.dto.assigned_to,
                quantity: t.quantity,
                isExisting: t.isExisting,
                currentQuantity: t.currentQuantity
              }))
            }
          };
        }

        Logger.debug(`Allocation validation passed for group ${groupKey}: ${effectiveAvailableQuantity} effective available >= ${group.totalQuantity} required`);
      }

      Logger.debug(`Pre-allocation validation completed successfully for all ${taskGroups.size} groups`);
      return { isValid: true };

    } catch (error) {
      Logger.error('Error during pre-allocation validation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        isValid: false,
        error: `Pre-allocation validation failed: ${errorMessage}`
      };
    }
  }
}