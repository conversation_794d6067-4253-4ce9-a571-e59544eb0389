import { FarmingPlanTaskTemplateDto } from '@app/modules/farming-plan-task-template/FarmingPlanTaskTemplate.dto';
import { IsString, IsOptional, IsEnum, IsDate, IsNumber, IsArray, IsDefined, IsUUID, IsJSON, Length, Min, Max } from 'class-validator';

export enum TaskType {
  SUBCULTURE = 'Subculture',
  ENV_PREPARATION = 'EnvPreparation',
  QUALITY_INSPECTION = 'QualityInspection',
  FINAL_QUALITY_INSPECTION = 'FinalQualityInspection',
  OTHER = 'Other'
}

export class CreateFarmingPlanTaskDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsString()
  @IsDefined()
  farming_plan_state: string;

  @IsString()
  @IsOptional()
  label?: string;

  @IsString()
  @IsOptional()
  image?: string;

  @IsString()
  @IsOptional()
  supplies_id?: string;

  @IsString()
  @IsOptional()
  supplies?: string;

  @IsDate()
  @IsOptional()
  start_date?: Date;

  @IsDate()
  @IsOptional()
  end_date?: Date;

  @IsString()
  @IsOptional()
  department_id?: string;

  @IsString()
  @IsOptional()
  index?: string;

  @IsString()
  @IsOptional()
  assigned_to?: string;

  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @IsOptional()
  enable_origin_tracing?: number;

  @IsNumber()
  @IsOptional()
  task_progress?: number;

  @IsNumber()
  @IsOptional()
  added_in_diary?: number;

  @IsString()
  @IsOptional()
  involved_in?: string;

  @IsString()
  @IsOptional()
  involved_in_users?: string;

  @IsOptional()
  priority_level?: string;

  @IsArray()
  @IsOptional()
  involve_in_users?: string[];

  @IsString()
  environment_template_id?: string;

  @IsString()
  @IsOptional()
  tag?: string;

  @IsString()
  @IsOptional()
  text_state?: string;

  @IsString()
  @IsOptional()
  text_plan?: string;

  @IsString()
  @IsOptional()
  text_assign_user?: string;

  @IsNumber()
  @IsOptional()
  is_template?: number;

  @IsString()
  @IsOptional()
  template_id?: string;

  @IsEnum(TaskType)
  @IsOptional()
  task_type?: TaskType;

  @IsArray()
  @IsOptional()
  item_list?: any[];

  @IsArray()
  @IsOptional()
  prod_quantity_list?: any[];

  @IsArray()
  @IsOptional()
  todo_list?: any[];

  @IsArray()
  @IsOptional()
  worksheet_list?: any[];

  @IsString()
  @IsOptional()
  previous_task_id?: string;

  @IsJSON()
  @IsOptional()
  task_chain_ids?: string;
}

export class UpdateFarmingPlanTaskDto extends CreateFarmingPlanTaskDto {

}

export class FilterFarmingPlanTaskDto {
  @IsString()
  @IsOptional()
  stateId?: string;

  @IsString()
  @IsOptional()
  templateId?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  assignedTo?: string;

  @IsEnum(TaskType)
  @IsOptional()
  taskType?: TaskType;

  @IsString()
  @IsOptional()
  previousTaskId?: string;

  @IsOptional()
  // cho phép JSON string hoặc mảng nested
  filters?: any;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  size?: number;
}



//interface cho API phân bổ công việc cho user
//logic:
//chọn state id, chọn multiple user (tabiot_customer_user, field assigned_to)
//chọn thời gian start_date, end_date
//mỗi user được assign 1 công việc, mỗi công việc có select từ template,
//ví dụ chỗ chọn nhân viên chọn 3 người, date range pick 20/4 tới 26/4, thì ở dưới table sẽ ra mỗi nv 7 dòng cv 20/4 tới 26/4, là 21 hàng.
//assign farming plan task cho user interface sẽ là array, mỗi object là một
//cong việc được phân bổ cho user
export class AssignFarmingPlanTaskDto {
  @IsString()
  @IsOptional()
  name?: string; // Optional task ID for updating existing tasks

  @IsString()
  @IsDefined()
  @IsUUID()
  assigned_to: string;

  @IsString()
  @IsDefined()
  template_id: FarmingPlanTaskTemplateDto["name"];

  //department_id
  @IsString()
  @IsDefined()
  department_id: string;

  @IsDate()
  @IsDefined()
  start_date: Date;

  @IsDate()
  @IsDefined()
  end_date: Date;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @IsDefined()
  exp_quantity: number; // Expected quantity for the task

  @IsEnum(TaskType)
  @IsOptional()
  task_type?: TaskType;

  @IsString()
  @IsOptional()
  previous_task_id?: string;
}


export class AssignFarmingPlanTaskDtoArray {
  @IsDefined()
  @IsString()
  state_id: string;

  @IsDefined()
  @IsString()
  department_id: string;

  @IsString()
  @IsOptional()
  production_plan_id?: string; // Optional production plan ID for allocation synchronization

  @IsDefined()
  @IsArray()
  tasks: AssignFarmingPlanTaskDto[];
}

export class WarehouseItemTaskUsedDto {
  @IsString()
  @IsDefined()
  name: string;

  @IsNumber()
  @IsDefined()
  quantity: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  iot_category_id?: string;

  @IsString()
  @IsOptional()
  @Length(1, 140, { message: 'Active UOM must be between 1 and 140 characters' })
  active_uom?: string;

  @IsNumber()
  @IsOptional()
  @Min(0.000000001, { message: 'Active conversion factor must be greater than 0' })
  @Max(999999999, { message: 'Active conversion factor must be less than 999999999' })
  active_conversion_factor?: number;
}


export class TaskManagementInfoDto {
  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  size?: number;

  @IsString()
  @IsOptional()
  stateId?: string;

  @IsString()
  @IsOptional()
  templateId?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  assignedTo?: string;

  @IsEnum(TaskType)
  @IsOptional()
  taskType?: TaskType;

  @IsString()
  @IsOptional()
  orderBy?: string;

  @IsOptional()
  filters?: any;
}

export class ProductionQuantityDto {
  @IsString()
  @IsDefined()
  name: string;

  @IsNumber()
  @IsDefined()
  quantity: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  product_id?: string;

  @IsString()
  @IsOptional()
  @Length(1, 140, { message: 'Active UOM must be between 1 and 140 characters' })
  active_uom?: string;

  @IsNumber()
  @IsOptional()
  @Min(0.000000001, { message: 'Active conversion factor must be greater than 0' })
  @Max(999999999, { message: 'Active conversion factor must be less than 999999999' })
  active_conversion_factor?: number;
}
